/**
 * Love项目主样式文件
 * 整合所有CSS样式，统一管理
 */

/* 导入原有样式文件 */
@import url('./style.css');
@import url('./pages.css');

/* 导入星空背景保障机制样式 */
@import url('./starry-background.css');

/* 新增的统一样式管理 */

/* 字体双源加载 - R2优先+本地降级架构 */

/* Courgette字体 - 保持原名称 */
@font-face {
    font-family: 'Courgette';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Courgette-Regular.woff2') format('woff2'),
         url('/fonts/compressed/Courgette-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Great Vibes字体 - 保持原名称 */
@font-face {
    font-family: 'Great Vibes';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/GreatVibes-Regular.woff2') format('woff2'),
         url('/fonts/compressed/GreatVibes-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* ZiXiaoHunGouYu字体 - 保持原名称 */
@font-face {
    font-family: 'ZiXiaoHunGouYu';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/ZiXiaoHunGouYu.woff2') format('woff2'),
         url('/fonts/compressed/ZiXiaoHunGouYu.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* ZiXiaoHunSanFen字体 - 保持原名称 */
@font-face {
    font-family: 'ZiXiaoHunSanFen';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/ZiXiaoHunSanFen.woff2') format('woff2'),
         url('/fonts/compressed/ZiXiaoHunSanFen.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* ZiHunXingYunFeiBai字体 - 保持原名称 */
@font-face {
    font-family: 'ZiHunXingYunFeiBai';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/ZiHunXingYun.woff2') format('woff2'),
         url('/fonts/compressed/ZiHunXingYun.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Dancing Script字体 - 多字重支持 */
@font-face {
    font-family: 'Dancing Script';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/DancingScript-Regular.woff2') format('woff2'),
         url('/fonts/compressed/DancingScript-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Dancing Script';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/DancingScript-Bold.woff2') format('woff2'),
         url('/fonts/compressed/DancingScript-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Poppins字体 - 多字重支持 */
@font-face {
    font-family: 'Poppins';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Poppins-Light.woff2') format('woff2'),
         url('/fonts/compressed/Poppins-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Poppins-Regular.woff2') format('woff2'),
         url('/fonts/compressed/Poppins-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Poppins-Medium.woff2') format('woff2'),
         url('/fonts/compressed/Poppins-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Poppins-SemiBold.woff2') format('woff2'),
         url('/fonts/compressed/Poppins-SemiBold.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Poppins-Bold.woff2') format('woff2'),
         url('/fonts/compressed/Poppins-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Inter字体 - 多字重支持 */
@font-face {
    font-family: 'Inter';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Inter-Light.woff2') format('woff2'),
         url('/fonts/compressed/Inter-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Inter';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Inter-Regular.woff2') format('woff2'),
         url('/fonts/compressed/Inter-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Inter';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Inter-Medium.woff2') format('woff2'),
         url('/fonts/compressed/Inter-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Inter';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Inter-SemiBold.woff2') format('woff2'),
         url('/fonts/compressed/Inter-SemiBold.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Inter';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/Inter-Bold.woff2') format('woff2'),
         url('/fonts/compressed/Inter-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Playfair Display字体 - 多字重支持 */
@font-face {
    font-family: 'Playfair Display';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/PlayfairDisplay-Regular.woff2') format('woff2'),
         url('/fonts/compressed/PlayfairDisplay-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Playfair Display';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/PlayfairDisplay-Medium.woff2') format('woff2'),
         url('/fonts/compressed/PlayfairDisplay-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Playfair Display';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/PlayfairDisplay-SemiBold.woff2') format('woff2'),
         url('/fonts/compressed/PlayfairDisplay-SemiBold.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Playfair Display';
    src: url('https://pub-b08ded3e27854ad38dc40954ccfa6520.r2.dev/love-website/fonts/PlayfairDisplay-Bold.woff2') format('woff2'),
         url('/fonts/compressed/PlayfairDisplay-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* 背景视频容器样式 */
.video-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    object-fit: cover;
}

/* 视频背景样式 - 注意：视频路径通过HTML的src属性设置，不在CSS中 */
.video-background video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 页面特定的视频背景类（用于JavaScript控制） */
.home-video-bg,
.together-days-video-bg,
.anniversary-video-bg,
.meetings-video-bg,
.memorial-video-bg {
    /* 这些类用于JavaScript动态设置视频源 */
}

/* 响应式设计优化 */
@media (max-width: 768px) {
    .video-background {
        display: none; /* 移动端隐藏视频背景以提升性能 */
    }
}

/* 性能优化 */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.lazy-load.loaded {
    opacity: 1;
}

/* 无障碍访问优化 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 打印样式 */
@media print {
    .video-background,
    .navigation,
    .floating-elements {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}
